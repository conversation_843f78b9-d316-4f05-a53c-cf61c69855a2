// Enhanced Animations and Interactions for Domus Leilões

document.addEventListener('DOMContentLoaded', function() {
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Number counter animation removed - numbers now stay fixed

    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for scroll animations
    const animatedElements = document.querySelectorAll('.service-card, .process-step, .testimonial-card, .stat-card, .highlight-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Enhanced button hover effects
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .btn-header-cta, .btn-form-submit');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Parallax effect removed as requested

    // Form validation and enhancement
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Add loading state to submit button
            const submitBtn = this.querySelector('.btn-form-submit');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class="fas fa-check"></i> Enviado com Sucesso!';
                submitBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    submitBtn.style.background = '';
                    this.reset();
                }, 3000);
            }, 2000);
        });
    }

    // Add floating animation to icons
    const icons = document.querySelectorAll('.service-icon, .stat-icon, .step-icon');
    icons.forEach((icon, index) => {
        icon.style.animationDelay = `${index * 0.5}s`;
    });

    // Enhanced card hover effects
    const cards = document.querySelectorAll('.service-card, .testimonial-card, .stat-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.02)';
            this.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
    });

    // Removed typing effect as requested

    // Add ripple effect to buttons
    function createRipple(event) {
        const button = event.currentTarget;
        const circle = document.createElement('span');
        const diameter = Math.max(button.clientWidth, button.clientHeight);
        const radius = diameter / 2;

        circle.style.width = circle.style.height = `${diameter}px`;
        circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
        circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
        circle.classList.add('ripple');

        const ripple = button.getElementsByClassName('ripple')[0];
        if (ripple) {
            ripple.remove();
        }

        button.appendChild(circle);
    }

    // Add ripple effect CSS
    const rippleCSS = `
        .ripple {
            position: absolute;
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 600ms linear;
            background-color: rgba(255, 255, 255, 0.6);
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    
    const style = document.createElement('style');
    style.textContent = rippleCSS;
    document.head.appendChild(style);

    // Apply ripple effect to buttons
    buttons.forEach(button => {
        button.addEventListener('click', createRipple);
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
    });

    // Section reveal animations removed to prevent scroll issues

    // ROI Calculator Functionality
    initROICalculator();

    console.log('🚀 Domus Leilões - Enhanced animations loaded successfully!');
});

// ROI Calculator Functions
function initROICalculator() {
    const investmentSlider = document.getElementById('investment-slider');
    const discountSlider = document.getElementById('discount-slider');
    const timeSlider = document.getElementById('time-slider');

    const investmentValue = document.getElementById('investment-value');
    const discountValue = document.getElementById('discount-value');
    const timeValue = document.getElementById('time-value');

    if (!investmentSlider) return; // Exit if calculator not on page

    // Update display values and calculations
    function updateCalculator() {
        const investment = parseInt(investmentSlider.value);
        const discount = parseInt(discountSlider.value);
        const timeMonths = parseInt(timeSlider.value);

        // Update input displays
        investmentValue.value = formatCurrency(investment);
        discountValue.value = discount + '%';
        timeValue.value = timeMonths + ' meses';

        // Calculate values
        const marketValue = investment / (1 - discount / 100);
        const savings = marketValue - investment;
        const appreciationRate = 0.08; // 8% annual appreciation
        const futureValue = marketValue * Math.pow(1 + appreciationRate, timeMonths / 12);
        const totalProfit = futureValue - investment;
        const roi = ((totalProfit / investment) * 100);

        // Update result displays
        document.getElementById('roi-percentage').textContent = roi.toFixed(0) + '%';
        document.getElementById('market-value').textContent = formatCurrency(marketValue);
        document.getElementById('savings-value').textContent = formatCurrency(savings);
        document.getElementById('profit-value').textContent = formatCurrency(totalProfit);

        // Update chart bars
        const auctionBar = document.getElementById('auction-bar');
        const marketBar = document.getElementById('market-bar');
        const auctionBarValue = document.getElementById('auction-bar-value');
        const marketBarValue = document.getElementById('market-bar-value');

        if (auctionBar && marketBar) {
            const maxValue = Math.max(investment, marketValue);
            const auctionWidth = (investment / maxValue) * 100;
            const marketWidth = (marketValue / maxValue) * 100;

            setTimeout(() => {
                auctionBar.style.width = auctionWidth + '%';
                marketBar.style.width = marketWidth + '%';
                auctionBarValue.textContent = formatCurrencyShort(investment);
                marketBarValue.textContent = formatCurrencyShort(marketValue);
            }, 100);
        }
    }

    // Format currency for display
    function formatCurrency(value) {
        return new Intl.NumberFormat('pt-BR', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    }

    // Format currency short (for chart)
    function formatCurrencyShort(value) {
        if (value >= 1000000) {
            return 'R$ ' + (value / 1000000).toFixed(1) + 'M';
        } else if (value >= 1000) {
            return 'R$ ' + (value / 1000).toFixed(0) + 'k';
        }
        return 'R$ ' + formatCurrency(value);
    }

    // Add event listeners
    investmentSlider.addEventListener('input', updateCalculator);
    discountSlider.addEventListener('input', updateCalculator);
    timeSlider.addEventListener('input', updateCalculator);

    // Initial calculation
    updateCalculator();

    // Animate bars on scroll
    const calculatorSection = document.querySelector('.roi-calculator');
    if (calculatorSection) {
        const calculatorObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(updateCalculator, 500);
                    calculatorObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.3 });

        calculatorObserver.observe(calculatorSection);
    }
}
