/**
 * Menu Mobile - Domus Leilões
 * Script para controlar o comportamento do menu mobile
 */

document.addEventListener("DOMContentLoaded", function () {
  // Elementos do menu
  const mobileMenuToggle = document.querySelector(".mobile-menu-toggle");
  const mobileNav = document.querySelector(".mobile-nav");
  const body = document.body;

  // Verificar se os elementos existem
  if (!mobileMenuToggle || !mobileNav) return;

  // Função para alternar o menu
  function toggleMobileMenu() {
    mobileMenuToggle.classList.toggle("active");
    mobileNav.classList.toggle("active");
    body.classList.toggle("menu-open"); // Impede o scroll quando o menu está aberto
  }

  // Event listener para o botão de menu
  mobileMenuToggle.addEventListener("click", toggleMobileMenu);

  // Fechar o menu ao clicar em um link
  const mobileNavLinks = mobileNav.querySelectorAll("a");
  mobileNavLinks.forEach((link) => {
    link.addEventListener("click", function () {
      toggleMobileMenu();
    });
  });

  // Adicionar classe ao header quando a página é scrollada
  const header = document.querySelector("header");
  if (header) {
    window.addEventListener("scroll", function () {
      if (window.scrollY > 50) {
        header.classList.add("scrolled");
      } else {
        header.classList.remove("scrolled");
      }
    });
  }
});
